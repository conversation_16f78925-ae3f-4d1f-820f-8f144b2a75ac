<template>
  <div class="page-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left">
        <font-awesome-icon icon="arrow-left" size="lg" color="#2a2a35" @click="goBack" class="back-icon" />
      </div>

      <div class="header-center">
        <h2 class="page-title">个人中心</h2>
      </div>

      <!-- <div class="header-right">
        <font-awesome-icon icon="cog" size="lg" color="#2a2a35" @click="openSettings" class="settings-icon" />
      </div> -->
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 用户头像和基本信息 -->
      <div class="profile-header">
        <div class="avatar-section">
          <div class="avatar-container">
            <img :src="formData.avatar || defaultAvatar" alt="用户头像" class="avatar" @click="changeAvatar" />
            <div class="avatar-edit-overlay" @click="changeAvatar">
              <font-awesome-icon icon="camera" color="#ffffff" />
            </div>
          </div>
          <div class="user-basic-info">
            <h3 class="username">{{ formData.username || '未设置用户名' }}</h3>
            <!-- 空间切换放在这里，替代用户ID -->
            <div class="workspace-selector" @click="showWorkspaceModal = true">
              <span class="workspace-name">{{ currentWorkspace.name }}</span>
              <font-awesome-icon icon="chevron-down" />
            </div>
          </div>
        </div>
      </div>

      <!-- 表单区域 -->
      <div class="form-container">
        <nut-form ref="profileForm" :model-value="formData" @validate="onValidate">
          <!-- 基本信息 -->
          <div class="section">
            <div class="section-header">
              <h4 class="section-title">基本信息</h4>
            </div>
            <div class="section-content">
              <nut-form-item
                label="用户名"
                prop="username"
                :rules="[
                  { required: true, message: '请输入用户名' },
                  { min: 2, max: 20, message: '用户名长度为2-20个字符' },
                ]"
              >
                <nut-input v-model="formData.username" placeholder="请输入用户名" clearable />
              </nut-form-item>

              <nut-form-item label="生日" prop="birthday" >
                <nut-input :model-value="formatDate(formData.birthday)" placeholder="请选择生日" readonly @click="showBirthdayPicker = true" />
                <nut-popup v-model:visible="showBirthdayPicker" position="bottom">
                  <nut-date-picker v-model="formData.birthday" type="date" title="选择生日" :min-date="new Date('1950-01-01')" :max-date="new Date()" @confirm="onBirthdayConfirm" @close="showBirthdayPicker = false" />
                </nut-popup>
              </nut-form-item>

              <nut-form-item label="个人简介" prop="bio">
                <nut-textarea v-model="formData.bio" placeholder="请输入个人简介" :rows="3" :max-length="200" show-word-limit />
              </nut-form-item>
            </div>
          </div>

          <!-- 职业信息 -->
          <div class="section">
            <div class="section-header">
              <h4 class="section-title">职业信息</h4>
            </div>
            <div class="section-content">
              <nut-form-item
                label="真实姓名"
                prop="realName"
                :rules="[
                  { min: 2, max: 10, message: '姓名长度为2-10个字符' },
                ]"
              >
                <nut-input v-model="formData.realName" placeholder="请输入真实姓名" clearable />
              </nut-form-item>
              <nut-form-item label="身份" prop="identity" >
                <nut-input :model-value="getIdentityLabel(formData.identity)" placeholder="请选择身份" readonly @click="showIdentityPicker = true" />
                <nut-popup v-model:visible="showIdentityPicker" position="bottom">
                  <nut-picker v-model="formData.identity" :columns="identityOptions" title="选择身份" @confirm="onIdentityConfirm" @close="showIdentityPicker = false" />
                </nut-popup>
              </nut-form-item>

              <nut-form-item
                label="公司"
                prop="company"
                :rules="[
                  { min: 2, max: 50, message: '公司名称长度为2-50个字符' },
                ]"
              >
                <nut-input v-model="formData.company" placeholder="请输入公司名称" clearable />
              </nut-form-item>

              <nut-form-item
                label="职位"
                prop="position"
                :rules="[
                  { min: 2, max: 30, message: '职位名称长度为2-30个字符' },
                ]"
              >
                <nut-input v-model="formData.position" placeholder="请输入职位" clearable />
              </nut-form-item>

              <nut-form-item label="工作证照" prop="workCertificate">
                <div class="certificate-upload">
                  <img v-if="formData.workCertificate" :src="formData.workCertificate" alt="工作证照" class="cert-image" @click="viewCertImage(formData.workCertificate)" />
                  <div v-else class="upload-placeholder" @click="uploadWorkCertificate">
                    <font-awesome-icon icon="plus" />
                    <span>上传工作证照</span>
                  </div>
                  <nut-button v-if="formData.workCertificate" @click="uploadWorkCertificate" type="default" size="small" class="change-image-btn"> 更换证照 </nut-button>
                </div>
              </nut-form-item>
            </div>
          </div>

          <!-- 统一提交按钮 -->
          <div class="submit-section">
            <nut-button type="primary" block size="large" :loading="submitting" @click="submitForm" class="submit-btn">
              {{ submitting ? '保存中...' : '保存信息' }}
            </nut-button>
          </div>
        </nut-form>
      </div>
    </div>

    <!-- 空间切换弹窗 -->
    <nut-popup v-model:visible="showWorkspaceModal" position="bottom" :style="{ height: '60%' }" round closeable close-icon-position="top-right">
      <div class="workspace-modal">
        <div class="modal-header">
          <h3>选择工作空间</h3>
        </div>
        <div class="modal-body">
          <div class="workspace-list">
            <div v-for="workspace in workspaces" :key="workspace.id" class="workspace-item" :class="{ active: workspace.id === currentWorkspace.id }" @click="switchWorkspace(workspace)">
              <div class="workspace-info">
                <div class="workspace-icon">
                  <font-awesome-icon :icon="workspace.icon" />
                </div>
                <div class="workspace-details">
                  <h4>{{ workspace.name }}</h4>
                  <p>{{ workspace.description }}</p>
                </div>
              </div>
              <div class="workspace-status">
                <font-awesome-icon v-if="workspace.id === currentWorkspace.id" icon="check-circle" color="#f94c30" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </nut-popup>

    <!-- 文件输入框（隐藏） -->
    <input ref="fileInput" type="file" accept="image/*" style="display: none" @change="handleFileUpload" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const profileForm = ref(null);

// 默认头像
const defaultAvatar = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzIiIGZpbGw9IiNmMGYwZjAiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyNCIgcj0iMTAiIGZpbGw9IiNjY2MiLz4KPHBhdGggZD0iTTEyIDUyYzAtMTEuMDQ2IDguOTU0LTIwIDIwLTIwczIwIDguOTU0IDIwIDIwSDEyeiIgZmlsbD0iI2NjYyIvPgo8L3N2Zz4K';

// 表单数据
const formData = reactive({
  userId: 'U001',
  username: '张三',
  avatar: '',
  realName: '张三',
  birthday: new Date('1990-01-01'),
  bio: '这个人很懒，什么都没有留下...',
  identity: 'employee',
  company: 'ABC科技有限公司',
  position: '前端开发工程师',
  workCertificate: '', // 简化为单个工作证照字段
});

// 身份选项
const identityOptions = [
  { text: '员工', value: 'employee' },
  { text: '管理者', value: 'manager' },
  { text: '主管', value: 'director' },
  { text: '管理员', value: 'admin' },
];

// 状态
const submitting = ref(false);
const showWorkspaceModal = ref(false);
const showBirthdayPicker = ref(false);
const showIdentityPicker = ref(false);

// 工作空间相关
const currentWorkspace = ref({
  id: 'ws1',
  name: '个人空间',
  description: '个人空间',
  icon: 'building',
});

const workspaces = ref([
  {
    id: 'ws1',
    name: '个人空间',
    description: '个人空间',
    icon: 'building',
  },
  {
    id: 'ws2',
    name: '项目A空间',
    description: '项目A专用工作空间',
    icon: 'project-diagram',
  },
  {
    id: 'ws3',
    name: '测试空间',
    description: '用于测试的工作空间',
    icon: 'flask',
  },
]);

// 文件上传相关
const fileInput = ref(null);
const currentUploadTarget = ref(null);

// 方法
const goBack = () => {
  router.back();
};

const openSettings = () => {
  console.log('打开设置');
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  if (typeof date === 'string') return date;
  return date.toISOString().split('T')[0];
};

// 获取身份标签
const getIdentityLabel = (value) => {
  const option = identityOptions.find((item) => item.value === value);
  return option ? option.text : '';
};

// 生日选择确认
const onBirthdayConfirm = (options) => {
  formData.birthday = options[0];
  showBirthdayPicker.value = false;
};

// 身份选择确认
const onIdentityConfirm = (options) => {
  formData.identity = options[0].value;
  showIdentityPicker.value = false;
};

// 表单验证
const onValidate = (prop, isValid, errorMsg) => {
  console.log('验证结果:', prop, isValid, errorMsg);
};

// 提交表单
const submitForm = async () => {
  try {
    submitting.value = true;

    // 验证表单
    const valid = await profileForm.value.validate();

    if (valid) {
      // 这里应该调用API保存数据
      console.log('提交表单数据:', formData);

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // 显示成功提示
      console.log('保存成功！');
    }
  } catch (error) {
    console.error('表单验证失败:', error);
    console.log('请检查表单信息');
  } finally {
    submitting.value = false;
  }
};

// 头像相关
const changeAvatar = () => {
  currentUploadTarget.value = 'avatar';
  fileInput.value.click();
};

const uploadWorkCertificate = () => {
  currentUploadTarget.value = 'workCertificate';
  fileInput.value.click();
};

const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件');
    return;
  }

  // 验证文件大小（最大5MB）
  if (file.size > 5 * 1024 * 1024) {
    alert('图片大小不能超过5MB');
    return;
  }

  // 创建文件预览URL
  const reader = new FileReader();
  reader.onload = (e) => {
    const imageUrl = e.target.result;

    if (currentUploadTarget.value === 'avatar') {
      formData.avatar = imageUrl;
    } else if (currentUploadTarget.value === 'workCertificate') {
      formData.workCertificate = imageUrl;
    }
  };
  reader.readAsDataURL(file);

  // 清空文件输入
  event.target.value = '';
};

// 证书图片查看
const viewCertImage = (imageUrl) => {
  window.open(imageUrl, '_blank');
};

// 工作空间管理
const switchWorkspace = (workspace) => {
  currentWorkspace.value = workspace;
  showWorkspaceModal.value = false;
  console.log('切换到工作空间:', workspace);
};

onMounted(() => {
  // 加载用户数据
  loadUserProfile();
});

const loadUserProfile = () => {
  console.log('加载用户资料');
};
</script>

<style scoped>
.page-container {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #ffffff;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header-left,
.header-right {
  width: 44px;
  display: flex;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.back-icon,
.settings-icon {
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.back-icon:hover,
.settings-icon:hover {
  background-color: #f0f0f0;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.profile-header {
  background: linear-gradient(135deg, #f94c30, #ff6b35);
  color: white;
  padding: 24px;
  border-radius: 16px;
  margin-bottom: 16px;
}

.avatar-section {
  display: flex;
  align-items: center;
}

.avatar-container {
  position: relative;
  margin-right: 16px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
}

.avatar-edit-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 28px;
  height: 28px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.avatar-edit-overlay:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.user-basic-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
}

/* 工作空间选择器样式调整 */
.workspace-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
  opacity: 0.9;
}

.workspace-selector:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.workspace-name {
  font-weight: 500;
}

.form-container {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section {
  border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
  border-bottom: none;
}

.section-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
}

.section-content {
  padding: 0 20px;
}

.certificate-upload {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cert-image {
  max-width: 200px;
  max-height: 150px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid #dee2e6;
}

.upload-placeholder {
  width: 200px;
  height: 120px;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s ease;
}

.upload-placeholder:hover {
  border-color: #f94c30;
  color: #f94c30;
}

.upload-placeholder span {
  margin-top: 8px;
  font-size: 14px;
}

.change-image-btn {
  margin-top: 8px;
}

.submit-section {
  padding: 20px;
  background-color: #f8f9fa;
}

.submit-btn {
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

/* 弹窗样式 */
.workspace-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
}

.modal-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* workspace-list样式已合并到.workspace-item中 */

.workspace-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 12px;
}

.workspace-item:hover {
  border-color: #f94c30;
  background-color: #fff5f4;
}

.workspace-item.active {
  border-color: #f94c30;
  background-color: #fff5f4;
}

.workspace-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.workspace-icon {
  width: 40px;
  height: 40px;
  background-color: #f94c30;
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.workspace-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
}

.workspace-details p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

.workspace-status {
  font-size: 20px;
}
</style>
