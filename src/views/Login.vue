<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>

    <!-- 主要内容 -->
    <div class="content">
      <!-- Logo -->
      <div class="logo-section">
        <div class="logo">
          <img src="../assets/images/logo.png" alt="logo" class="logo-image">
        </div>
        <h1 class="app-title">采购端工作台</h1>
      </div>

      <!-- 登录表单 -->
      <div class="login-form-section">
        <div class="form-header">
          <h2>欢迎登录</h2>
          <p>请输入您的账号信息</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <!-- 用户名输入 -->
          <div class="form-group">
            <nut-input
              v-model="loginForm.username"
              placeholder="请输入用户名/手机号"
              :border="false"
              class="form-input"
              :error="errors.username"
            >
              <template #left>
                <font-awesome-icon icon="user" class="input-icon" />
              </template>
            </nut-input>
            <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
          </div>

          <!-- 密码输入 -->
          <div class="form-group">
            <nut-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :border="false"
              class="form-input"
              :error="errors.password"
            >
              <template #left>
                <font-awesome-icon icon="lock" class="input-icon" />
              </template>
            </nut-input>
            <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
          </div>

          <!-- 记住密码和忘记密码 -->
          <div class="form-options">
            <nut-checkbox v-model="loginForm.rememberMe">
              <span class="remember-text">记住密码</span>
            </nut-checkbox>
            <a href="#" class="forgot-password" @click.prevent="handleForgotPassword">
              忘记密码？
            </a>
          </div>

          <!-- 登录按钮 -->
          <nut-button
            type="primary"
            size="large"
            block
            :loading="isLoading"
            @click="handleLogin"
            class="login-btn"
          >
            {{ isLoading ? '登录中...' : '登录' }}
          </nut-button>

          <!-- 注册链接 -->
          <div class="register-section">
            <span class="register-text">还没有账号？</span>
            <a href="#" class="register-link" @click.prevent="handleRegister">
              立即注册
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuth } from '../composables/useAuth'

const router = useRouter()
const route = useRoute()
const { login } = useAuth()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// 表单验证错误
const errors = reactive({
  username: '',
  password: ''
})

// 加载状态
const isLoading = ref(false)

// 表单验证
const validateForm = () => {
  errors.username = ''
  errors.password = ''
  
  let isValid = true
  
  if (!loginForm.username.trim()) {
    errors.username = '请输入用户名或手机号'
    isValid = false
  }
  
  if (!loginForm.password.trim()) {
    errors.password = '请输入密码'
    isValid = false
  } else if (loginForm.password.length < 6) {
    errors.password = '密码长度不能少于6位'
    isValid = false
  }
  
  return isValid
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 模拟登录成功
    const userData = {
      id: '1',
      username: loginForm.username,
      name: loginForm.username === 'admin' ? '管理员' : '张工程师',
      company: '上海电气自动化研究所',
      avatar: '',
      token: 'mock-jwt-token-' + Date.now()
    }
    
    // 调用登录方法
    await login(userData)
    
    // 获取重定向路径
    const redirectPath = route.query.redirect || '/home'
    
    // 跳转到目标页面
    router.push(redirectPath)
    
  } catch (error) {
    console.error('登录失败:', error)
    // 这里可以显示错误提示
    alert('登录失败，请检查用户名和密码')
  } finally {
    isLoading.value = false
  }
}

// 处理忘记密码
const handleForgotPassword = () => {
  // TODO: 实现忘记密码功能
  alert('忘记密码功能待实现')
}

// 处理注册
const handleRegister = () => {
  // TODO: 实现注册功能
  alert('注册功能待实现')
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 16px;
}

.logo-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.app-title {
  font-size: 24px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0 0 8px 0;
}

.form-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.login-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-input {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
}

.input-icon {
  color: #999;
  margin-right: 12px;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 4px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.remember-text {
  font-size: 14px;
  color: #666;
}

.forgot-password {
  font-size: 14px;
  color: #f94c30;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-btn {
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border: none;
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(249, 76, 48, 0.3);
}

.register-section {
  text-align: center;
}

.register-text {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
}

.register-link {
  font-size: 14px;
  color: #f94c30;
  text-decoration: none;
  font-weight: 500;
}

.register-link:hover {
  text-decoration: underline;
}
</style>
