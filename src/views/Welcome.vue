<template>
  <div class="welcome-container">
    <!-- 背景 -->
    <div class="background-overlay">
    </div>
    
    <!-- 主要内容 -->
    <div class="content">
      <!-- Logo -->
      <div class="logo-section">
        <div class="logo">
          <img src="../assets/images/logo.png" alt="logo" class="logo-image">
        </div>
      </div>
      
      <!-- 标题文字 -->
      <div class="title-section">
        <h1 class="main-title">采购端工作台</h1>
      </div>
      
      <!-- 登录按钮 -->
      <div class="action-section">
        <nut-button 
          type="primary" 
          size="large" 
          block 
          @click="goToLogin"
          class="login-btn"
        >
          注册/登录
        </nut-button>
      </div>
      
      <!-- 协议同意 -->
      <div class="agreement-section">
        <nut-checkbox v-model="agreedToTerms">
          <span style="color: #fff;">同意</span>
          <span class="link" @click="showUserAgreement">《用户协议》</span>
          <span style="color: #fff;">和</span>
          <span class="link" @click="showPrivacyPolicy">《隐私政策》</span>
        </nut-checkbox>
      </div>
    </div>
    
    <!-- 装饰元素 -->
    <div class="decoration-elements">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 协议同意状态
const agreedToTerms = ref(false)

// 跳转到登录/主页
const goToLogin = () => {
  if (!agreedToTerms.value) {
    // 使用 NutUI 的 Toast 组件提示
    // TODO: 这里需要集成 Toast 组件
    alert('请先同意用户协议和隐私政策')
    return
  }
  
  // 这里可以跳转到登录页面，现在直接跳转到主页
  router.push('/home')
}

// 显示用户协议
const showUserAgreement = () => {
  // TODO: 实现用户协议弹窗
  console.log('显示用户协议')
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  // TODO: 实现隐私政策弹窗
  console.log('显示隐私政策')
}
</script>

<style scoped>
.welcome-container {
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.background-overlay {
  background-image: url('../assets/images/welcome_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 320px;
  padding: 40px 24px;
  text-align: center;
}


.logo {
  height: 100px;
 
  position: relative;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}


.title-section {
  margin-bottom: 480px;
}

.main-title {
  font-size: 28px;
  color: #fff;
}

.subtitle {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}

.action-section {
  margin-bottom: 32px;
}

.login-btn {
  height: 50px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  border: none;
  box-shadow: 0 4px 20px rgba(249, 76, 48, 0.3);
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(249, 76, 48, 0.4);
}

.agreement-section {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

.link {
  color: #f94c30;
  text-decoration: underline;
  cursor: pointer;
}

.link:hover {
  color: #e0431b;
}

.decoration-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
}

.circle-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  top: -100px;
  right: -100px;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, #2a2a35, #1a1a23);
  bottom: -75px;
  left: -75px;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  top: 20%;
  left: -50px;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .content {
    padding: 32px 20px;
  }
  
  .main-title {
    font-size: 28px;
  }
  
  .logo {
    width: 100px;
    height: 100px;
  }
}
</style>