<template>
  <div class="inquiry-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <div class="header-left">
        <font-awesome-icon icon="arrow-left" size="lg" color="#2a2a35" @click="goBack" class="back-icon" />
      </div>

      <div class="header-center">
        <h2 class="page-title">询价器</h2>
      </div>

      <div class="header-right">
        <!-- 空白占位 -->
      </div>
    </div>

    <!-- 询价物料列表 -->
    <div class="inquiry-content">
      <!-- 添加物料方式 -->
      <!-- <div class="add-materials-section">

        <div class="add-methods-single">
          <div class="method-card-single" @click="openManualDialog">
            <div class="method-icon-single">
              <font-awesome-icon icon="plus" />
            </div>
            <span class="method-text-single">添加物料</span>
          </div>
        </div>
      </div> -->

      <nut-button block type="primary" @click="openManualDialog" class="add-material-button">
        <font-awesome-icon icon="plus" />
        添加物料
      </nut-button>

      <div class="materials-section">
        <div class="section-header">
          <h2 class="section-title">
            <font-awesome-icon icon="list" />
            询价物料清单
          </h2>
          <span class="material-count">{{ materials.length }} 项</span>
        </div>

        <!-- 空状态 -->
        <div v-if="materials.length === 0" class="empty-state">
          <div class="empty-icon">
            <font-awesome-icon icon="box-open" size="3x" />
          </div>
          <h3>暂无询价物料</h3>
          <p>请使用上方按钮添加物料</p>
        </div>

        <!-- 物料列表 -->
        <div v-else class="materials-list">
          <div v-for="(material, index) in materials" :key="material.id" class="material-item">
            <div class="material-header">
              <div class="material-info">
                <h3 class="material-name">
                  <nut-tag color="#f94c30"> {{ material.brand }} </nut-tag>
                  {{ material.name }}
                </h3>
                <div class="material-meta">
                  <span class="material-model"
                    >{{ material.model }} <nut-tag color="#f94c30" plain type="primary" style="margin-left: 5px">x{{ material.quantity }}</nut-tag></span
                  >
                </div>
              </div>
              <div class="material-actions">
                <button @click="editMaterial(index)" class="action-btn edit">
                  <font-awesome-icon icon="edit" />
                </button>
                <button @click="removeMaterial(index)" class="action-btn delete">
                  <font-awesome-icon icon="times" />
                </button>
              </div>
            </div>

            <div class="material-details">
              <div class="detail-tags">
                <div class="detail-tag">
                  <font-awesome-icon icon="calendar" />
                  <span>{{ getDaysFromNow(material.expectedDate) }}天</span>
                </div>
                <div class="detail-tag" :class="{ alternative: material.acceptAlternative }">
                  <font-awesome-icon :icon="material.acceptAlternative ? 'check-circle' : 'times-circle'" />
                  <span>{{ material.acceptAlternative ? '接受平替' : '不接受平替' }}</span>
                </div>
              </div>

              <div v-if="material.remark" class="material-remark">
                <font-awesome-icon icon="comment" />
                <span>{{ material.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 询价设置 -->
      <div v-if="materials.length > 0" class="inquiry-settings">
        <h2 class="section-title">
          <font-awesome-icon icon="cogs" />
          询价设置
        </h2>

        <div class="settings-card">
          <div class="setting-item">
            <label class="setting-label">报价截止时间</label>
            <nut-cell :title="quotationDeadline ? formatDate(quotationDeadline) : '请选择截止时间'" :desc="quotationDeadline ? '点击修改' : '设定供应商报价的截止时间'" is-link />
            <nut-popup>
              <nut-date-picker v-model="quotationDeadline" :min-date="minDate" :max-date="maxDate" type="datetime" title="选择截止时间"> </nut-date-picker>
            </nut-popup>
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div v-if="materials.length > 0" class="submit-section">
        <nut-button type="primary" size="large" block :loading="submitting" :disabled="!quotationDeadline" @click="submitInquiry">
          <font-awesome-icon icon="paper-plane" />
          提交询价单 ({{ materials.length }}项物料)
        </nut-button>
        <p class="submit-note">提交后将生成询价单，供应商可开始报价</p>
      </div>
    </div>

    <!-- 搜索物料弹窗 -->
    <nut-popup v-model:visible="searchDialogVisible" position="bottom" round>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>搜索物料</h3>
          <button @click="searchDialogVisible = false" class="close-btn">
            <font-awesome-icon icon="times" />
          </button>
        </div>
        <div class="search-section">
          <nut-searchbar v-model="searchKeyword" placeholder="输入物料名称、型号或品牌" @search="searchMaterials" />
        </div>
        <div class="search-results">
          <div v-if="searchResults.length === 0 && searchKeyword" class="no-results">
            <p>未找到相关物料</p>
          </div>
          <div v-for="item in searchResults" :key="item.id" class="search-item" @click="selectSearchResult(item)">
            <div class="search-item-info">
              <h4 class="search-item-name">{{ item.name }}</h4>
              <div class="search-item-details">
                <span class="search-item-model">型号：{{ item.model }}</span>
                <span class="search-item-brand">品牌：{{ item.brand }}</span>
              </div>
            </div>
            <div class="search-item-action">
              <font-awesome-icon icon="plus" />
            </div>
          </div>
        </div>
      </div>
    </nut-popup>

    <!-- 手动添加弹窗（合并智能解析） -->
    <nut-popup v-model:visible="manualDialogVisible" position="bottom" round>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>{{ editingIndex !== null ? '编辑物料' : '添加物料' }}</h3>
          <button @click="closeManualDialog" class="close-btn">
            <font-awesome-icon icon="times" />
          </button>
        </div>

        <!-- 智能解析区域 -->
        <div v-if="editingIndex === null" class="smart-analysis-section">
          <h4 class="analysis-title">
            <font-awesome-icon icon="magic" />
            智能解析
          </h4>
          <nut-textarea v-model="analysisText" placeholder="请输入包含物料信息的文字，例如：需要购买西门子6ES7 214-1BD23-0XB0 PLC控制器 10个，交期2024年3月15日&#10;&#10;系统将自动识别物料名称、型号、品牌、数量等信息并填充下方表单" rows="4" max-length="1000" show-word-limit />
          <div class="analysis-action">
            <nut-button type="primary" size="small" :loading="analyzing" @click="analyzeTextAndFill" :disabled="!analysisText.trim()">
              <font-awesome-icon icon="magic" />
              智能解析并填充表单
            </nut-button>
          </div>
        </div>

        <!-- 手动填写表单区域 -->
        <div class="form-section">
          <nut-form ref="materialForm" :rules="formRules">
            <nut-form-item label="物料名称" prop="name" required>
              <nut-input v-model="currentMaterial.name" placeholder="请输入物料名称" />
            </nut-form-item>
            <nut-form-item label="型号" prop="model" required>
              <nut-input v-model="currentMaterial.model" placeholder="请输入型号" />
            </nut-form-item>
            <nut-form-item label="品牌" prop="brand" required>
              <nut-input v-model="currentMaterial.brand" placeholder="请输入品牌" />
            </nut-form-item>
            <nut-form-item label="期望交期（天）" prop="expectedDays" required>
              <nut-input v-model="currentMaterial.expectedDays" placeholder="请输入期望交期天数，如：7" type="number" />
            </nut-form-item>
            <nut-form-item label="数量" prop="quantity" required>
              <nut-input v-model="currentMaterial.quantity" placeholder="请输入数量" type="number" />
            </nut-form-item>
            <nut-form-item label="接受平替">
              <nut-switch v-model="currentMaterial.acceptAlternative" />
            </nut-form-item>
            <nut-form-item label="备注">
              <nut-textarea v-model="currentMaterial.remark" placeholder="请输入备注信息（可选）" rows="3" />
            </nut-form-item>
          </nut-form>
          <div class="form-actions">
            <nut-button @click="closeManualDialog">取消</nut-button>
            <nut-button type="primary" @click="saveMaterial">
              {{ editingIndex !== null ? '保存修改' : '添加物料' }}
            </nut-button>
          </div>
        </div>
      </div>
    </nut-popup>

    <!-- BOM导入弹窗 -->
    <nut-popup v-model:visible="bomDialogVisible" position="bottom" round>
      <div class="dialog-content">
        <div class="dialog-header">
          <h3>导入BOM</h3>
          <button @click="bomDialogVisible = false" class="close-btn">
            <font-awesome-icon icon="times" />
          </button>
        </div>
        <div class="upload-section">
          <div class="upload-area" @click="triggerFileUpload">
            <font-awesome-icon icon="cloud-upload-alt" size="2x" />
            <p>点击上传BOM文件</p>
            <p class="upload-tip">支持 Excel (.xlsx, .xls) 和 CSV 格式</p>
          </div>
          <input ref="fileInput" type="file" accept=".xlsx,.xls,.csv" @change="handleBomFile" style="display: none" />
          <div class="upload-template">
            <nut-button size="small" @click="downloadTemplate">
              <font-awesome-icon icon="download" />
              下载模板
            </nut-button>
          </div>
        </div>
      </div>
    </nut-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 响应式数据
const materials = ref([]);
const quotationDeadline = ref(null);
const inquiryDescription = ref('');
const submitting = ref(false);

// 弹窗状态
const searchDialogVisible = ref(false);
const manualDialogVisible = ref(false);
const bomDialogVisible = ref(false);

// 搜索相关
const searchKeyword = ref('');
const searchResults = ref([]);

// 表单相关
const materialForm = ref(null);
const fileInput = ref(null);
const currentMaterial = ref({
  name: '',
  model: '',
  brand: '',
  expectedDays: '',
  quantity: '',
  acceptAlternative: false,
  remark: '',
});
const editingIndex = ref(null);

// 智能解析相关
const analysisText = ref('');
const analyzing = ref(false);

// 日期范围
const minDate = computed(() => new Date());
const maxDate = computed(() => {
  const date = new Date();
  date.setMonth(date.getMonth() + 6);
  return date;
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入物料名称' }],
  model: [{ required: true, message: '请输入型号' }],
  brand: [{ required: true, message: '请输入品牌' }],
  expectedDays: [
    { required: true, message: '请输入期望交期天数' },
    { pattern: /^\d+$/, message: '期望交期必须为正整数' },
  ],
  quantity: [
    { required: true, message: '请输入数量' },
    { pattern: /^\d+$/, message: '数量必须为正整数' },
  ],
};

// 方法
const goBack = () => {
  router.back();
};

const openSearchDialog = () => {
  searchDialogVisible.value = true;
  searchKeyword.value = '';
  searchResults.value = [];
};

const openManualDialog = () => {
  editingIndex.value = null;
  resetCurrentMaterial();
  manualDialogVisible.value = true;
};

const openBomDialog = () => {
  bomDialogVisible.value = true;
};

const closeManualDialog = () => {
  manualDialogVisible.value = false;
  editingIndex.value = null;
  resetCurrentMaterial();
  analysisText.value = ''; // 清空智能解析文本
};

const resetCurrentMaterial = () => {
  currentMaterial.value = {
    name: '',
    model: '',
    brand: '',
    expectedDays: '',
    quantity: '',
    acceptAlternative: false,
    remark: '',
  };
};

const searchMaterials = () => {
  // 模拟搜索结果
  const mockData = [
    { id: 1, name: '西门子PLC控制器', model: '6ES7 214-1BD23-0XB0', brand: '西门子' },
    { id: 2, name: '施耐德接触器', model: 'LC1D09M7', brand: '施耐德' },
    { id: 3, name: 'ABB变频器', model: 'ACS550-01-03A3-4', brand: 'ABB' },
    { id: 4, name: '欧姆龙传感器', model: 'E2E-X5ME1', brand: '欧姆龙' },
    { id: 5, name: '三菱电机PLC', model: 'FX3U-16MR/ES-A', brand: '三菱电机' },
  ];

  if (searchKeyword.value.trim()) {
    searchResults.value = mockData.filter((item) => item.name.includes(searchKeyword.value) || item.model.includes(searchKeyword.value) || item.brand.includes(searchKeyword.value));
  } else {
    searchResults.value = mockData;
  }
};

const selectSearchResult = (item) => {
  currentMaterial.value = {
    name: item.name,
    model: item.model,
    brand: item.brand,
    expectedDate: null,
    quantity: '1',
    acceptAlternative: false,
    remark: '',
  };
  searchDialogVisible.value = false;
  manualDialogVisible.value = true;
};

const saveMaterial = async () => {
  try {
    await materialForm.value.validate();

    // 将期望交期天数转换为实际日期
    const expectedDate = new Date();
    expectedDate.setDate(expectedDate.getDate() + parseInt(currentMaterial.value.expectedDays));

    const material = {
      id: editingIndex.value !== null ? materials.value[editingIndex.value].id : Date.now(),
      ...currentMaterial.value,
      expectedDate: expectedDate, // 保存计算后的日期
    };

    // 删除expectedDays字段，因为我们只需要保存expectedDate
    delete material.expectedDays;

    if (editingIndex.value !== null) {
      materials.value[editingIndex.value] = material;
    } else {
      materials.value.push(material);
    }

    closeManualDialog();
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

const editMaterial = (index) => {
  editingIndex.value = index;
  const material = { ...materials.value[index] };

  // 将日期转换回天数以便编辑
  if (material.expectedDate) {
    const daysFromNow = getDaysFromNow(material.expectedDate);
    material.expectedDays = daysFromNow.toString();
  }

  currentMaterial.value = material;
  manualDialogVisible.value = true;
};

const removeMaterial = (index) => {
  materials.value.splice(index, 1);
};

const triggerFileUpload = () => {
  fileInput.value.click();
};

const handleBomFile = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    console.log('上传BOM文件:', files[0]);

    // 模拟解析BOM文件并添加物料
    const mockBomData = [
      {
        id: Date.now() + 1,
        name: '从BOM导入-西门子PLC',
        model: '6ES7 214-1BD23-0XB0',
        brand: '西门子',
        expectedDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        quantity: '2',
        acceptAlternative: false,
        remark: 'BOM导入',
      },
      {
        id: Date.now() + 2,
        name: '从BOM导入-施耐德接触器',
        model: 'LC1D09M7',
        brand: '施耐德',
        expectedDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
        quantity: '5',
        acceptAlternative: true,
        remark: 'BOM导入',
      },
    ];

    materials.value.push(...mockBomData);
    bomDialogVisible.value = false;

    // 重置文件输入
    event.target.value = '';
  }
};

const downloadTemplate = () => {
  console.log('下载BOM模板');
  // 这里可以实现模板下载逻辑
};

const analyzeTextAndFill = async () => {
  if (!analysisText.value.trim()) {
    return;
  }

  analyzing.value = true;

  // 模拟AI解析
  setTimeout(() => {
    // 模拟解析结果并自动填充表单
    const mockResult = {
      name: '西门子PLC控制器',
      model: '6ES7 214-1BD23-0XB0',
      brand: '西门子',
      expectedDays: '7',
      quantity: '10',
      acceptAlternative: false,
      remark: '智能解析自动识别',
    };

    // 自动填充当前物料表单
    currentMaterial.value = { ...mockResult };
    analyzing.value = false;

    // 可以选择性地清空解析文本
    // analysisText.value = '';
  }, 2000);
};

const submitInquiry = async () => {
  if (!quotationDeadline.value) {
    alert('请设置报价截止时间');
    return;
  }

  submitting.value = true;

  try {
    // 模拟提交询价单
    const inquiryData = {
      materials: materials.value,
      quotationDeadline: quotationDeadline.value,
      description: inquiryDescription.value,
      createdAt: new Date(),
    };

    console.log('提交询价单:', inquiryData);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 提交成功，跳转到询价管理页面
    router.push('/inquiry-management');
  } catch (error) {
    console.error('提交询价单失败:', error);
  } finally {
    submitting.value = false;
  }
};

const formatDate = (date, format = 'YYYY-MM-DD HH:mm') => {
  if (!date) return '';
  const d = new Date(date);

  if (format === 'YYYY-MM-DD') {
    return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
  }

  return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0') + ' ' + String(d.getHours()).padStart(2, '0') + ':' + String(d.getMinutes()).padStart(2, '0');
};

const getDaysFromNow = (date) => {
  if (!date) return 0;
  const today = new Date();
  const expectedDate = new Date(date);

  // 将时间设置为0点来比较日期
  today.setHours(0, 0, 0, 0);
  expectedDate.setHours(0, 0, 0, 0);

  const diffTime = expectedDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays); // 返回0或正数
};

onMounted(() => {
  // 初始化时设置默认截止时间（7天后）
  const defaultDeadline = new Date();
  defaultDeadline.setDate(defaultDeadline.getDate() + 7);
  quotationDeadline.value = defaultDeadline;

  // 预填一些示例数据方便测试
  searchMaterials();

  // 添加一些假数据用于展示
  materials.value = [
    {
      id: 1,
      name: '西门子PLC控制器',
      model: '6ES7 214-1BD23-0XB0',
      brand: '西门子',
      expectedDate: new Date('2024-04-15'),
      quantity: '2',
      acceptAlternative: false,
      remark: '用于自动化生产线控制系统',
    },
    {
      id: 2,
      name: '施耐德接触器',
      model: 'LC1D09M7',
      brand: '施耐德',
      expectedDate: new Date('2024-04-20'),
      quantity: '5',
      acceptAlternative: true,
      remark: '电机启动控制用',
    },
    {
      id: 3,
      name: 'ABB变频器',
      model: 'ACS550-01-03A3-4',
      brand: 'ABB',
      expectedDate: new Date('2024-04-25'),
      quantity: '1',
      acceptAlternative: false,
      remark: '风机调速控制，要求原装正品',
    },
    {
      id: 4,
      name: '欧姆龙光电传感器',
      model: 'E2E-X5ME1',
      brand: '欧姆龙',
      expectedDate: new Date('2024-04-18'),
      quantity: '10',
      acceptAlternative: true,
      remark: '检测工件到位信号',
    },
    {
      id: 5,
      name: '三菱电机伺服电机',
      model: 'HG-KN23J-S100',
      brand: '三菱电机',
      expectedDate: new Date('2024-05-01'),
      quantity: '2',
      acceptAlternative: false,
      remark: '精密定位控制，需要带编码器',
    },
    {
      id: 6,
      name: '台达触摸屏',
      model: 'DOP-B07S515',
      brand: '台达',
      expectedDate: new Date('2024-04-22'),
      quantity: '1',
      acceptAlternative: true,
      remark: '7寸彩色触摸屏，用于人机界面',
    },
  ];
});
</script>

<style scoped>
.inquiry-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 导航栏样式 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left,
.header-right {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-center {
  flex: 1;
  text-align: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.back-icon {
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.back-icon:hover {
  background-color: #f0f0f0;
}

.inquiry-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-material-button {
  margin-bottom: 16px;
}

.material-count {
  font-size: 14px;
  color: #f94c30;
  background: rgba(249, 76, 48, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.materials-section,
.add-materials-section,
.inquiry-settings {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.materials-section {
  background: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.empty-icon {
  margin-bottom: 16px;
  color: #ddd;
}

.empty-state h3 {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
}

.materials-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: transparent;
  border-radius: 0;
  overflow: visible;
}

.material-item {
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  border: none;
}

.material-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f8f9fa;
}

.material-info {
  flex: 1;
}

.material-name {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.material-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.material-model {
  font-weight: 500;
}

.material-divider {
  color: #ddd;
}

.material-brand {
  color: #f94c30;
  font-weight: 500;
}

.material-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.action-btn.edit {
  background: rgba(249, 76, 48, 0.1);
  color: #f94c30;
}

.action-btn.edit:hover {
  background: rgba(249, 76, 48, 0.2);
}

.action-btn.delete {
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.action-btn.delete:hover {
  background: rgba(255, 71, 87, 0.2);
}

.material-details {
  border-top: none;
  padding-top: 0;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: #f8f9fa;
  border-radius: 20px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.detail-tag.alternative {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.detail-tag svg {
  width: 12px;
  height: 12px;
}

.material-remark {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 10px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  margin-top: 8px;
}

.material-remark svg {
  width: 12px;
  height: 12px;
  margin-top: 2px;
  color: #999;
}

.add-methods-single {
  display: flex;
  justify-content: center;
  padding: 4px 0;
}

.method-card-single {
  width: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #f94c30, #e0431b);
  color: white;
  box-shadow: 0 4px 12px rgba(249, 76, 48, 0.2);
}

.method-card-single:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(249, 76, 48, 0.3);
}

.method-icon-single {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.method-text-single {
  font-size: 16px;
  font-weight: 600;
  color: white;
  line-height: 1.2;
}

.settings-card {
  padding: 16px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  font-size: 14px;
  font-weight: 500;
  color: #2a2a35;
  display: block;
  margin-bottom: 8px;
}

.submit-section {
  text-align: center;
  padding: 20px;
}

.submit-note {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.dialog-content {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f0f0f0;
}

.search-section {
  margin-bottom: 20px;
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
}

.no-results {
  text-align: center;
  color: #999;
  padding: 20px;
}

.search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.search-item:hover {
  border-color: #f94c30;
  background: rgba(249, 76, 48, 0.05);
}

.search-item-info {
  flex: 1;
}

.search-item-name {
  font-size: 14px;
  font-weight: 500;
  color: #2a2a35;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.search-item-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.search-item-model,
.search-item-brand {
  font-size: 12px;
  color: #666;
}

.search-item-brand {
  color: #f94c30;
  font-weight: 500;
}

.search-item-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: #f94c30;
}

.form-section {
  padding-top: 8px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.form-actions .nut-button {
  flex: 1;
}

.upload-section {
  text-align: center;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #f94c30;
  background: rgba(249, 76, 48, 0.05);
}

.upload-area p {
  margin: 8px 0 4px;
  font-size: 14px;
  color: #2a2a35;
}

.upload-tip {
  font-size: 12px !important;
  color: #999 !important;
}

.upload-template {
  margin-top: 16px;
}

.analysis-section {
  padding-top: 8px;
}

.analysis-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.analysis-actions .nut-button {
  flex: 1;
}

/* 智能解析区域样式 */
.smart-analysis-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.analysis-title {
  font-size: 16px;
  font-weight: 600;
  color: #2a2a35;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysis-title svg {
  color: #f94c30;
}

.analysis-action {
  margin-top: 12px;
  text-align: center;
}

.divider {
  margin: 20px 0;
  text-align: center;
  position: relative;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e9ecef;
}

.divider span {
  background: white;
  padding: 0 16px;
  color: #6c757d;
  font-size: 14px;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .method-card-single {
    width: 100%;
    max-width: 300px;
    padding: 14px;
  }

  .method-icon-single {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .method-text-single {
    font-size: 14px;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-content {
    padding: 16px;
  }
}
</style>
