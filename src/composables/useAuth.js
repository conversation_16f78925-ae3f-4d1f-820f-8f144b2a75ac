import { ref, computed } from 'vue'

// 全局状态
const user = ref(null)
const token = ref(null)
const isLoading = ref(false)

// 从 localStorage 恢复登录状态
const initAuth = () => {
  const savedUser = localStorage.getItem('user')
  const savedToken = localStorage.getItem('token')
  
  if (savedUser && savedToken) {
    try {
      user.value = JSON.parse(savedUser)
      token.value = savedToken
    } catch (error) {
      console.error('恢复登录状态失败:', error)
      clearAuth()
    }
  }
}

// 清除认证信息
const clearAuth = () => {
  user.value = null
  token.value = null
  localStorage.removeItem('user')
  localStorage.removeItem('token')
}

// 保存认证信息
const saveAuth = (userData, userToken) => {
  user.value = userData
  token.value = userToken
  localStorage.setItem('user', JSON.stringify(userData))
  localStorage.setItem('token', userToken)
}

export function useAuth() {
  // 计算属性
  const isLoggedIn = computed(() => !!user.value && !!token.value)
  const currentUser = computed(() => user.value)
  
  // 登录方法
  const login = async (userData) => {
    isLoading.value = true
    
    try {
      // 这里可以添加实际的API调用
      // const response = await api.login(credentials)
      
      // 模拟API响应
      const userToken = userData.token || 'mock-jwt-token-' + Date.now()
      
      // 保存用户信息和token
      saveAuth(userData, userToken)
      
      console.log('登录成功:', userData)
      return { success: true, user: userData }
      
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  // 登出方法
  const logout = async () => {
    isLoading.value = true
    
    try {
      // 这里可以添加实际的API调用
      // await api.logout()
      
      // 清除本地存储的认证信息
      clearAuth()
      
      console.log('登出成功')
      return { success: true }
      
    } catch (error) {
      console.error('登出失败:', error)
      // 即使API调用失败，也要清除本地认证信息
      clearAuth()
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  // 检查token是否有效
  const checkAuth = async () => {
    if (!token.value) {
      return false
    }
    
    try {
      // 这里可以添加实际的token验证API调用
      // const response = await api.verifyToken(token.value)
      
      // 模拟token验证
      return true
      
    } catch (error) {
      console.error('Token验证失败:', error)
      clearAuth()
      return false
    }
  }
  
  // 更新用户信息
  const updateUser = (userData) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }
  
  // 获取认证头
  const getAuthHeader = () => {
    return token.value ? { Authorization: `Bearer ${token.value}` } : {}
  }
  
  return {
    // 状态
    user: currentUser,
    isLoggedIn,
    isLoading,
    
    // 方法
    login,
    logout,
    checkAuth,
    updateUser,
    getAuthHeader,
    initAuth
  }
}

// 初始化认证状态（在应用启动时调用）
initAuth()
