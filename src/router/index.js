import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'Welcome',
    component: () => import('../views/Welcome.vue'),
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('../views/Home.vue'),
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
  },
  {
    path: '/inquiry',
    name: 'Inquiry',
    component: () => import('../views/Inquiry.vue'),
  },
  {
    path: '/inquiry-management',
    name: 'InquiryManagement',
    component: () => import('../views/InquiryManagement.vue'),
  },
  {
    path: '/inquiry-share/:inquiryNumber',
    name: 'InquiryShare',
    component: () => import('../views/InquiryShare.vue'),
  },
  {
    path: '/purchase-request',
    name: 'PurchaseRequest',
    component: () => import('../views/PurchaseRequest.vue'),
  },
  {
    path: '/purchase-management',
    name: 'PurchaseManagement',
    component: () => import('../views/PurchaseManagement.vue'),
  },
  {
    path: '/receipt-management',
    name: 'ReceiptManagement',
    component: () => import('../views/ReceiptManagement.vue'),
  },
  {
    path: '/reconciliation-management',
    name: 'ReconciliationManagement',
    component: () => import('../views/ReconciliationManagement.vue'),
  },
  {
    path: '/payment-management',
    name: 'PaymentManagement',
    component: () => import('../views/PaymentManagement.vue'),
  },
  {
    path: '/address-info',
    name: 'AddressInfo',
    component: () => import('../views/AddressInfo.vue'),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
