/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AIServiceModal: typeof import('./src/components/AIServiceModal.vue')['default']
    NutConfigProvider: typeof import('@nutui/nutui')['ConfigProvider']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabBar: typeof import('./src/components/TabBar.vue')['default']
  }
}
