import{r,c as n,g as N,o as i,a as t,d as e,e as g,t as a,F as y,h as k,i as I,j as B,u as V,k as A,n as R}from"./index-DiND1jQw.js";import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";const T={class:"profile-section"},D={class:"profile-text"},E={class:"username"},F={class:"company"},H={class:"menu-section"},M=["onClick"],P={class:"menu-title"},j={class:"action-section"},L={class:"action-title"},Q={__name:"Navbar",props:{isVisible:{type:Boolean,default:!1}},emits:["close"],setup(z,{emit:p}){const u=V(),f=p,d=r({name:"张三",company:"某某自动化设备有限公司",avatar:""}),m=r(5),b=r([{title:"询价管理",path:"/inquiry-management",icon:"question-circle"},{title:"采购申请",path:"/purchase-request",icon:"shopping-cart"},{title:"采购管理",path:"/purchase-management",icon:"box"},{title:"收货管理",path:"/receipt-management",icon:"truck"},{title:"对账管理",path:"/reconciliation-management",icon:"clipboard-list"},{title:"付款管理",path:"/payment-management",icon:"credit-card"},{title:"地址信息",path:"/address-info",icon:"map-marker-alt"}]),v=()=>{f("close")},C=c=>{u.push(c),v()},q=()=>{u.push("/profile"),v()},_=()=>{console.log("企业操作")},o=()=>{console.log("消息通知")};return(c,s)=>{const $=g("nut-avatar"),l=g("font-awesome-icon"),w=g("nut-badge");return z.isVisible?(i(),n("div",{key:0,class:"navbar-overlay",onClick:v},[t("div",{class:"navbar-container",onClick:s[0]||(s[0]=B(()=>{},["stop"]))},[t("div",T,[t("div",{class:"profile-info",onClick:q},[e($,{size:"60",src:d.value.avatar,icon:d.value.avatar?null:"my","bg-color":"#f94c30"},null,8,["src","icon"]),t("div",D,[t("div",E,a(d.value.name||"未登录用户"),1),t("div",F,a(d.value.company||"请完善企业信息"),1)]),e(l,{icon:"chevron-right",size:"sm",color:"#999"})])]),t("div",H,[(i(!0),n(y,null,k(b.value,h=>(i(),n("div",{class:"menu-item",key:h.path,onClick:vt=>C(h.path)},[e(l,{icon:h.icon,size:"lg",color:"#f94c30"},null,8,["icon"]),t("span",P,a(h.title),1),e(l,{icon:"chevron-right",size:"sm",color:"#999"})],8,M))),128))]),t("div",j,[t("div",{class:"action-item",onClick:_},[e(l,{icon:"store",size:"lg",color:"#f94c30"}),s[1]||(s[1]=t("span",{class:"action-title"},"企业操作",-1)),e(l,{icon:"chevron-right",size:"sm",color:"#999"})]),t("div",{class:"action-item",onClick:o},[e(l,{icon:"bell",size:"lg",color:"#f94c30"}),t("span",L,[s[2]||(s[2]=t("span",null,"消息通知",-1)),m.value>0?(i(),I(w,{key:0,value:m.value,style:{"margin-left":"14px"}},null,8,["value"])):N("",!0)]),e(l,{icon:"chevron-right",size:"sm",color:"#999"})])])])])):N("",!0)}}},S=x(Q,[["__scopeId","data-v-dfd1e628"]]),U={class:"home-container"},G={class:"header"},J={class:"header-left"},K={class:"header-right"},O={class:"main-content"},W={class:"quick-actions"},X={class:"actions-grid"},Y=["onClick"],Z={class:"action-icon"},tt={class:"action-title"},st={class:"data-overview"},et={class:"stats-grid"},it={class:"stat-value"},ot={class:"stat-label"},nt={class:"recent-activities"},at={class:"activity-list"},ct={class:"activity-icon"},lt={class:"activity-content"},rt={class:"activity-title"},ut={class:"activity-time"},dt={__name:"Home",setup(z){const p=V(),u=r(!1),f=r([{key:"inquiry",title:"新建询价",icon:"question-circle",path:"/inquiry-management"},{key:"purchase",title:"采购申请",icon:"shopping-cart",path:"/purchase-request"},{key:"receipt",title:"收货管理",icon:"truck",path:"/receipt-management"},{key:"payment",title:"付款管理",icon:"credit-card",path:"/payment-management"}]),d=r([{key:"pending-inquiry",label:"待处理询价",value:"12"},{key:"pending-purchase",label:"待审批采购",value:"8"},{key:"pending-receipt",label:"待收货",value:"5"},{key:"pending-payment",label:"待付款",value:"3"}]),m=r([{id:1,title:"询价单 #INQ202401001 已提交",time:"2小时前",status:"待处理",statusClass:"status-pending",icon:"question-circle"},{id:2,title:"采购申请 #PUR202401001 已审批",time:"4小时前",status:"已通过",statusClass:"status-approved",icon:"shopping-cart"},{id:3,title:"收货单 #REC202401001 已确认",time:"1天前",status:"已完成",statusClass:"status-completed",icon:"truck"}]),b=()=>{u.value=!0},v=()=>{u.value=!1},C=()=>{p.push("/inquiry")},q=_=>{p.push(_)};return A(()=>{}),(_,o)=>{const c=g("font-awesome-icon");return i(),n("div",U,[t("div",G,[t("div",J,[e(c,{icon:"bars",size:"lg",color:"#2a2a35",onClick:b,class:"menu-icon"})]),o[0]||(o[0]=t("div",{class:"header-center"},[t("h2",{class:"page-title"},"采购工作台")],-1)),t("div",K,[e(c,{icon:"question-circle",size:"lg",color:"#2a2a35",onClick:C,class:"inquiry-icon"})])]),t("div",O,[t("div",W,[o[1]||(o[1]=t("h3",{class:"section-title"},"快捷操作",-1)),t("div",X,[(i(!0),n(y,null,k(f.value,s=>(i(),n("div",{class:"action-item",key:s.key,onClick:$=>q(s.path)},[t("div",Z,[e(c,{icon:s.icon,size:"lg",color:"#f94c30"},null,8,["icon"])]),t("span",tt,a(s.title),1)],8,Y))),128))])]),t("div",st,[o[2]||(o[2]=t("h3",{class:"section-title"},"数据概览",-1)),t("div",et,[(i(!0),n(y,null,k(d.value,s=>(i(),n("div",{class:"stat-item",key:s.key},[t("div",it,a(s.value),1),t("div",ot,a(s.label),1)]))),128))])]),t("div",nt,[o[3]||(o[3]=t("h3",{class:"section-title"},"最近操作",-1)),t("div",at,[(i(!0),n(y,null,k(m.value,s=>(i(),n("div",{class:"activity-item",key:s.id},[t("div",ct,[e(c,{icon:s.icon,size:"lg",color:"#f94c30"},null,8,["icon"])]),t("div",lt,[t("div",rt,a(s.title),1),t("div",ut,a(s.time),1)]),t("div",{class:R(["activity-status",s.statusClass])},a(s.status),3)]))),128))])])]),e(S,{isVisible:u.value,onClose:v},null,8,["isVisible"])])}}},_t=x(dt,[["__scopeId","data-v-9351e13a"]]);export{_t as default};
