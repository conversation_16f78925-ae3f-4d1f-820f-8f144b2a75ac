import{r as u,c as p,a as s,b,d as t,w as l,e as a,u as g,o as f,f as _}from"./index-DiND1jQw.js";import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";const x="/mp-client/assets/logo-CH3BrtAb.png",y={class:"welcome-container"},V={class:"content"},C={class:"action-section"},w={class:"agreement-section"},B={__name:"Welcome",setup(N){const n=g(),e=u(!1),c=()=>{if(!e.value){alert("请先同意用户协议和隐私政策");return}n.push("/home")},i=()=>{console.log("显示用户协议")},r=()=>{console.log("显示隐私政策")};return(T,o)=>{const d=a("nut-button"),m=a("nut-checkbox");return f(),p("div",y,[o[5]||(o[5]=s("div",{class:"background-overlay"},null,-1)),s("div",V,[o[4]||(o[4]=b('<div class="logo-section" data-v-b003d75b><div class="logo" data-v-b003d75b><img src="'+x+'" alt="logo" class="logo-image" data-v-b003d75b></div></div><div class="title-section" data-v-b003d75b><h1 class="main-title" data-v-b003d75b>采购端工作台</h1></div>',2)),s("div",C,[t(d,{type:"primary",size:"large",block:"",onClick:c,class:"login-btn"},{default:l(()=>o[1]||(o[1]=[_(" 注册/登录 ")])),_:1})]),s("div",w,[t(m,{modelValue:e.value,"onUpdate:modelValue":o[0]||(o[0]=v=>e.value=v)},{default:l(()=>[o[2]||(o[2]=s("span",{style:{color:"#fff"}},"同意",-1)),s("span",{class:"link",onClick:i},"《用户协议》"),o[3]||(o[3]=s("span",{style:{color:"#fff"}},"和",-1)),s("span",{class:"link",onClick:r},"《隐私政策》")]),_:1},8,["modelValue"])])]),o[6]||(o[6]=s("div",{class:"decoration-elements"},[s("div",{class:"circle circle-1"}),s("div",{class:"circle circle-2"}),s("div",{class:"circle circle-3"})],-1))])}}},U=k(B,[["__scopeId","data-v-b003d75b"]]);export{U as default};
