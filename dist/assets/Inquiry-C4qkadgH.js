import{r as d,m as P,k as ce,c as m,a as t,d as a,e as p,g as x,w as i,f as c,t as r,F as X,h as N,u as me,o as v,n as pe}from"./index-DiND1jQw.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";const _e={class:"inquiry-container"},fe={class:"header"},ye={class:"header-left"},De={class:"inquiry-content"},be={class:"materials-section"},ge={class:"section-header"},ke={class:"section-title"},he={class:"material-count"},xe={key:0,class:"empty-state"},we={class:"empty-icon"},qe={key:1,class:"materials-list"},Ve={class:"material-header"},Ce={class:"material-info"},Me={class:"material-name"},Be={class:"material-meta"},Ae={class:"material-model"},Se={class:"material-actions"},Ue=["onClick"],Ee=["onClick"],Fe={class:"material-details"},Le={class:"detail-tags"},Ye={class:"detail-tag"},ze={key:0,class:"material-remark"},Oe={key:0,class:"inquiry-settings"},Ie={class:"section-title"},Pe={class:"settings-card"},Xe={class:"setting-item"},Ne={key:1,class:"submit-section"},Te={class:"dialog-content"},$e={class:"dialog-header"},He={class:"search-section"},Re={class:"search-results"},Ke={key:0,class:"no-results"},Ge=["onClick"],Je={class:"search-item-info"},je={class:"search-item-name"},Qe={class:"search-item-details"},We={class:"search-item-model"},Ze={class:"search-item-brand"},et={class:"search-item-action"},tt={class:"dialog-content"},at={class:"dialog-header"},lt={key:0,class:"smart-analysis-section"},nt={class:"analysis-title"},st={class:"analysis-action"},ot={class:"form-section"},it={class:"form-actions"},dt={class:"dialog-content"},ut={class:"dialog-header"},rt={class:"upload-section"},ct={class:"upload-template"},mt={__name:"Inquiry",setup(pt){const E=me(),u=d([]),f=d(null),T=d(""),B=d(!1),w=d(!1),g=d(!1),q=d(!1),D=d(""),V=d([]),F=d(null),L=d(null),o=d({name:"",model:"",brand:"",expectedDays:"",quantity:"",acceptAlternative:!1,remark:""}),_=d(null),k=d(""),A=d(!1),$=P(()=>new Date),H=P(()=>{const n=new Date;return n.setMonth(n.getMonth()+6),n}),R={name:[{required:!0,message:"请输入物料名称"}],model:[{required:!0,message:"请输入型号"}],brand:[{required:!0,message:"请输入品牌"}],expectedDays:[{required:!0,message:"请输入期望交期天数"},{pattern:/^\d+$/,message:"期望交期必须为正整数"}],quantity:[{required:!0,message:"请输入数量"},{pattern:/^\d+$/,message:"数量必须为正整数"}]},K=()=>{E.back()},G=()=>{_.value=null,Y(),g.value=!0},S=()=>{g.value=!1,_.value=null,Y(),k.value=""},Y=()=>{o.value={name:"",model:"",brand:"",expectedDays:"",quantity:"",acceptAlternative:!1,remark:""}},z=()=>{const n=[{id:1,name:"西门子PLC控制器",model:"6ES7 214-1BD23-0XB0",brand:"西门子"},{id:2,name:"施耐德接触器",model:"LC1D09M7",brand:"施耐德"},{id:3,name:"ABB变频器",model:"ACS550-01-03A3-4",brand:"ABB"},{id:4,name:"欧姆龙传感器",model:"E2E-X5ME1",brand:"欧姆龙"},{id:5,name:"三菱电机PLC",model:"FX3U-16MR/ES-A",brand:"三菱电机"}];D.value.trim()?V.value=n.filter(e=>e.name.includes(D.value)||e.model.includes(D.value)||e.brand.includes(D.value)):V.value=n},J=n=>{o.value={name:n.name,model:n.model,brand:n.brand,expectedDate:null,quantity:"1",acceptAlternative:!1,remark:""},w.value=!1,g.value=!0},j=async()=>{try{await F.value.validate();const n=new Date;n.setDate(n.getDate()+parseInt(o.value.expectedDays));const e={id:_.value!==null?u.value[_.value].id:Date.now(),...o.value,expectedDate:n};delete e.expectedDays,_.value!==null?u.value[_.value]=e:u.value.push(e),S()}catch(n){console.error("表单验证失败:",n)}},Q=n=>{_.value=n;const e={...u.value[n]};if(e.expectedDate){const s=O(e.expectedDate);e.expectedDays=s.toString()}o.value=e,g.value=!0},W=n=>{u.value.splice(n,1)},Z=()=>{L.value.click()},ee=n=>{const e=n.target.files;if(e&&e.length>0){console.log("上传BOM文件:",e[0]);const s=[{id:Date.now()+1,name:"从BOM导入-西门子PLC",model:"6ES7 214-1BD23-0XB0",brand:"西门子",expectedDate:new Date(Date.now()+7*24*60*60*1e3),quantity:"2",acceptAlternative:!1,remark:"BOM导入"},{id:Date.now()+2,name:"从BOM导入-施耐德接触器",model:"LC1D09M7",brand:"施耐德",expectedDate:new Date(Date.now()+10*24*60*60*1e3),quantity:"5",acceptAlternative:!0,remark:"BOM导入"}];u.value.push(...s),q.value=!1,n.target.value=""}},te=()=>{console.log("下载BOM模板")},ae=async()=>{k.value.trim()&&(A.value=!0,setTimeout(()=>{const n={name:"西门子PLC控制器",model:"6ES7 214-1BD23-0XB0",brand:"西门子",expectedDays:"7",quantity:"10",acceptAlternative:!1,remark:"智能解析自动识别"};o.value={...n},A.value=!1},2e3))},le=async()=>{if(!f.value){alert("请设置报价截止时间");return}B.value=!0;try{const n={materials:u.value,quotationDeadline:f.value,description:T.value,createdAt:new Date};console.log("提交询价单:",n),await new Promise(e=>setTimeout(e,2e3)),E.push("/inquiry-management")}catch(n){console.error("提交询价单失败:",n)}finally{B.value=!1}},ne=(n,e="YYYY-MM-DD HH:mm")=>{if(!n)return"";const s=new Date(n);return e==="YYYY-MM-DD"?s.getFullYear()+"-"+String(s.getMonth()+1).padStart(2,"0")+"-"+String(s.getDate()).padStart(2,"0"):s.getFullYear()+"-"+String(s.getMonth()+1).padStart(2,"0")+"-"+String(s.getDate()).padStart(2,"0")+" "+String(s.getHours()).padStart(2,"0")+":"+String(s.getMinutes()).padStart(2,"0")},O=n=>{if(!n)return 0;const e=new Date,s=new Date(n);e.setHours(0,0,0,0),s.setHours(0,0,0,0);const y=s-e,C=Math.ceil(y/(1e3*60*60*24));return Math.max(0,C)};return ce(()=>{const n=new Date;n.setDate(n.getDate()+7),f.value=n,z(),u.value=[{id:1,name:"西门子PLC控制器",model:"6ES7 214-1BD23-0XB0",brand:"西门子",expectedDate:new Date("2024-04-15"),quantity:"2",acceptAlternative:!1,remark:"用于自动化生产线控制系统"},{id:2,name:"施耐德接触器",model:"LC1D09M7",brand:"施耐德",expectedDate:new Date("2024-04-20"),quantity:"5",acceptAlternative:!0,remark:"电机启动控制用"},{id:3,name:"ABB变频器",model:"ACS550-01-03A3-4",brand:"ABB",expectedDate:new Date("2024-04-25"),quantity:"1",acceptAlternative:!1,remark:"风机调速控制，要求原装正品"},{id:4,name:"欧姆龙光电传感器",model:"E2E-X5ME1",brand:"欧姆龙",expectedDate:new Date("2024-04-18"),quantity:"10",acceptAlternative:!0,remark:"检测工件到位信号"},{id:5,name:"三菱电机伺服电机",model:"HG-KN23J-S100",brand:"三菱电机",expectedDate:new Date("2024-05-01"),quantity:"2",acceptAlternative:!1,remark:"精密定位控制，需要带编码器"},{id:6,name:"台达触摸屏",model:"DOP-B07S515",brand:"台达",expectedDate:new Date("2024-04-22"),quantity:"1",acceptAlternative:!0,remark:"7寸彩色触摸屏，用于人机界面"}]}),(n,e)=>{const s=p("font-awesome-icon"),y=p("nut-button"),C=p("nut-tag"),se=p("nut-cell"),oe=p("nut-date-picker"),M=p("nut-popup"),ie=p("nut-searchbar"),I=p("nut-textarea"),h=p("nut-input"),b=p("nut-form-item"),de=p("nut-switch"),ue=p("nut-form");return v(),m("div",_e,[t("div",fe,[t("div",ye,[a(s,{icon:"arrow-left",size:"lg",color:"#2a2a35",onClick:K,class:"back-icon"})]),e[15]||(e[15]=t("div",{class:"header-center"},[t("h2",{class:"page-title"},"询价器")],-1)),e[16]||(e[16]=t("div",{class:"header-right"},null,-1))]),t("div",De,[a(y,{block:"",type:"primary",onClick:G,class:"add-material-button"},{default:i(()=>[a(s,{icon:"plus"}),e[17]||(e[17]=c(" 添加物料 "))]),_:1}),t("div",be,[t("div",ge,[t("h2",ke,[a(s,{icon:"list"}),e[18]||(e[18]=c(" 询价物料清单 "))]),t("span",he,r(u.value.length)+" 项",1)]),u.value.length===0?(v(),m("div",xe,[t("div",we,[a(s,{icon:"box-open",size:"3x"})]),e[19]||(e[19]=t("h3",null,"暂无询价物料",-1)),e[20]||(e[20]=t("p",null,"请使用上方按钮添加物料",-1))])):(v(),m("div",qe,[(v(!0),m(X,null,N(u.value,(l,U)=>(v(),m("div",{key:l.id,class:"material-item"},[t("div",Ve,[t("div",Ce,[t("h3",Me,[a(C,{color:"#f94c30"},{default:i(()=>[c(r(l.brand),1)]),_:2},1024),c(" "+r(l.name),1)]),t("div",Be,[t("span",Ae,[c(r(l.model)+" ",1),a(C,{color:"#f94c30",plain:"",type:"primary",style:{"margin-left":"5px"}},{default:i(()=>[c("x"+r(l.quantity),1)]),_:2},1024)])])]),t("div",Se,[t("button",{onClick:re=>Q(U),class:"action-btn edit"},[a(s,{icon:"edit"})],8,Ue),t("button",{onClick:re=>W(U),class:"action-btn delete"},[a(s,{icon:"times"})],8,Ee)])]),t("div",Fe,[t("div",Le,[t("div",Ye,[a(s,{icon:"calendar"}),t("span",null,r(O(l.expectedDate))+"天",1)]),t("div",{class:pe(["detail-tag",{alternative:l.acceptAlternative}])},[a(s,{icon:l.acceptAlternative?"check-circle":"times-circle"},null,8,["icon"]),t("span",null,r(l.acceptAlternative?"接受平替":"不接受平替"),1)],2)]),l.remark?(v(),m("div",ze,[a(s,{icon:"comment"}),t("span",null,r(l.remark),1)])):x("",!0)])]))),128))]))]),u.value.length>0?(v(),m("div",Oe,[t("h2",Ie,[a(s,{icon:"cogs"}),e[21]||(e[21]=c(" 询价设置 "))]),t("div",Pe,[t("div",Xe,[e[22]||(e[22]=t("label",{class:"setting-label"},"报价截止时间",-1)),a(se,{title:f.value?ne(f.value):"请选择截止时间",desc:f.value?"点击修改":"设定供应商报价的截止时间","is-link":""},null,8,["title","desc"]),a(M,null,{default:i(()=>[a(oe,{modelValue:f.value,"onUpdate:modelValue":e[0]||(e[0]=l=>f.value=l),"min-date":$.value,"max-date":H.value,type:"datetime",title:"选择截止时间"},null,8,["modelValue","min-date","max-date"])]),_:1})])])])):x("",!0),u.value.length>0?(v(),m("div",Ne,[a(y,{type:"primary",size:"large",block:"",loading:B.value,disabled:!f.value,onClick:le},{default:i(()=>[a(s,{icon:"paper-plane"}),c(" 提交询价单 ("+r(u.value.length)+"项物料) ",1)]),_:1},8,["loading","disabled"]),e[23]||(e[23]=t("p",{class:"submit-note"},"提交后将生成询价单，供应商可开始报价",-1))])):x("",!0)]),a(M,{visible:w.value,"onUpdate:visible":e[3]||(e[3]=l=>w.value=l),position:"bottom",round:""},{default:i(()=>[t("div",Te,[t("div",$e,[e[24]||(e[24]=t("h3",null,"搜索物料",-1)),t("button",{onClick:e[1]||(e[1]=l=>w.value=!1),class:"close-btn"},[a(s,{icon:"times"})])]),t("div",He,[a(ie,{modelValue:D.value,"onUpdate:modelValue":e[2]||(e[2]=l=>D.value=l),placeholder:"输入物料名称、型号或品牌",onSearch:z},null,8,["modelValue"])]),t("div",Re,[V.value.length===0&&D.value?(v(),m("div",Ke,e[25]||(e[25]=[t("p",null,"未找到相关物料",-1)]))):x("",!0),(v(!0),m(X,null,N(V.value,l=>(v(),m("div",{key:l.id,class:"search-item",onClick:U=>J(l)},[t("div",Je,[t("h4",je,r(l.name),1),t("div",Qe,[t("span",We,"型号："+r(l.model),1),t("span",Ze,"品牌："+r(l.brand),1)])]),t("div",et,[a(s,{icon:"plus"})])],8,Ge))),128))])])]),_:1},8,["visible"]),a(M,{visible:g.value,"onUpdate:visible":e[12]||(e[12]=l=>g.value=l),position:"bottom",round:""},{default:i(()=>[t("div",tt,[t("div",at,[t("h3",null,r(_.value!==null?"编辑物料":"添加物料"),1),t("button",{onClick:S,class:"close-btn"},[a(s,{icon:"times"})])]),_.value===null?(v(),m("div",lt,[t("h4",nt,[a(s,{icon:"magic"}),e[26]||(e[26]=c(" 智能解析 "))]),a(I,{modelValue:k.value,"onUpdate:modelValue":e[4]||(e[4]=l=>k.value=l),placeholder:`请输入包含物料信息的文字，例如：需要购买西门子6ES7 214-1BD23-0XB0 PLC控制器 10个，交期2024年3月15日

系统将自动识别物料名称、型号、品牌、数量等信息并填充下方表单`,rows:"4","max-length":"1000","show-word-limit":""},null,8,["modelValue"]),t("div",st,[a(y,{type:"primary",size:"small",loading:A.value,onClick:ae,disabled:!k.value.trim()},{default:i(()=>[a(s,{icon:"magic"}),e[27]||(e[27]=c(" 智能解析并填充表单 "))]),_:1},8,["loading","disabled"])])])):x("",!0),t("div",ot,[a(ue,{ref_key:"materialForm",ref:F,rules:R},{default:i(()=>[a(b,{label:"物料名称",prop:"name",required:""},{default:i(()=>[a(h,{modelValue:o.value.name,"onUpdate:modelValue":e[5]||(e[5]=l=>o.value.name=l),placeholder:"请输入物料名称"},null,8,["modelValue"])]),_:1}),a(b,{label:"型号",prop:"model",required:""},{default:i(()=>[a(h,{modelValue:o.value.model,"onUpdate:modelValue":e[6]||(e[6]=l=>o.value.model=l),placeholder:"请输入型号"},null,8,["modelValue"])]),_:1}),a(b,{label:"品牌",prop:"brand",required:""},{default:i(()=>[a(h,{modelValue:o.value.brand,"onUpdate:modelValue":e[7]||(e[7]=l=>o.value.brand=l),placeholder:"请输入品牌"},null,8,["modelValue"])]),_:1}),a(b,{label:"期望交期（天）",prop:"expectedDays",required:""},{default:i(()=>[a(h,{modelValue:o.value.expectedDays,"onUpdate:modelValue":e[8]||(e[8]=l=>o.value.expectedDays=l),placeholder:"请输入期望交期天数，如：7",type:"number"},null,8,["modelValue"])]),_:1}),a(b,{label:"数量",prop:"quantity",required:""},{default:i(()=>[a(h,{modelValue:o.value.quantity,"onUpdate:modelValue":e[9]||(e[9]=l=>o.value.quantity=l),placeholder:"请输入数量",type:"number"},null,8,["modelValue"])]),_:1}),a(b,{label:"接受平替"},{default:i(()=>[a(de,{modelValue:o.value.acceptAlternative,"onUpdate:modelValue":e[10]||(e[10]=l=>o.value.acceptAlternative=l)},null,8,["modelValue"])]),_:1}),a(b,{label:"备注"},{default:i(()=>[a(I,{modelValue:o.value.remark,"onUpdate:modelValue":e[11]||(e[11]=l=>o.value.remark=l),placeholder:"请输入备注信息（可选）",rows:"3"},null,8,["modelValue"])]),_:1})]),_:1},512),t("div",it,[a(y,{onClick:S},{default:i(()=>e[28]||(e[28]=[c("取消")])),_:1}),a(y,{type:"primary",onClick:j},{default:i(()=>[c(r(_.value!==null?"保存修改":"添加物料"),1)]),_:1})])])])]),_:1},8,["visible"]),a(M,{visible:q.value,"onUpdate:visible":e[14]||(e[14]=l=>q.value=l),position:"bottom",round:""},{default:i(()=>[t("div",dt,[t("div",ut,[e[29]||(e[29]=t("h3",null,"导入BOM",-1)),t("button",{onClick:e[13]||(e[13]=l=>q.value=!1),class:"close-btn"},[a(s,{icon:"times"})])]),t("div",rt,[t("div",{class:"upload-area",onClick:Z},[a(s,{icon:"cloud-upload-alt",size:"2x"}),e[30]||(e[30]=t("p",null,"点击上传BOM文件",-1)),e[31]||(e[31]=t("p",{class:"upload-tip"},"支持 Excel (.xlsx, .xls) 和 CSV 格式",-1))]),t("input",{ref_key:"fileInput",ref:L,type:"file",accept:".xlsx,.xls,.csv",onChange:ee,style:{display:"none"}},null,544),t("div",ct,[a(y,{size:"small",onClick:te},{default:i(()=>[a(s,{icon:"download"}),e[32]||(e[32]=c(" 下载模板 "))]),_:1})])])])]),_:1},8,["visible"])])}}},ft=ve(mt,[["__scopeId","data-v-1282bca0"]]);export{ft as default};
